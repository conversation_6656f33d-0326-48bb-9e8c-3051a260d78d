export const cashVarianceData = {
  categories: [
    'Primary Care', 'Specialty Care', 'Urgent Care', 'Diagnostics', 
    'Surgery', 'Physical Therapy', 'Mental Health', 'Administration'
  ],
  metrics: [
    'Cash Inflows', 'Cash Outflows', 'Net Cash Flow', 'Working Capital'
  ],
  data: [
    // Primary Care
    [
      { actual: 450000, budget: 420000, variance: 7.1 },
      { actual: 380000, budget: 360000, variance: 5.6 },
      { actual: 70000, budget: 60000, variance: 16.7 },
      { actual: 220000, budget: 200000, variance: 10.0 }
    ],
    // Specialty Care
    [
      { actual: 380000, budget: 400000, variance: -5.0 },
      { actual: 320000, budget: 330000, variance: -3.0 },
      { actual: 60000, budget: 70000, variance: -14.3 },
      { actual: 180000, budget: 190000, variance: -5.3 }
    ],
    // Urgent Care
    [
      { actual: 320000, budget: 300000, variance: 6.7 },
      { actual: 270000, budget: 250000, variance: 8.0 },
      { actual: 50000, budget: 50000, variance: 0.0 },
      { actual: 150000, budget: 140000, variance: 7.1 }
    ],
    // Diagnostics
    [
      { actual: 280000, budget: 290000, variance: -3.4 },
      { actual: 230000, budget: 240000, variance: -4.2 },
      { actual: 50000, budget: 50000, variance: 0.0 },
      { actual: 130000, budget: 135000, variance: -3.7 }
    ],
    // Surgery
    [
      { actual: 250000, budget: 270000, variance: -7.4 },
      { actual: 210000, budget: 220000, variance: -4.5 },
      { actual: 40000, budget: 50000, variance: -20.0 },
      { actual: 120000, budget: 130000, variance: -7.7 }
    ],
    // Physical Therapy
    [
      { actual: 180000, budget: 170000, variance: 5.9 },
      { actual: 150000, budget: 140000, variance: 7.1 },
      { actual: 30000, budget: 30000, variance: 0.0 },
      { actual: 90000, budget: 85000, variance: 5.9 }
    ],
    // Mental Health
    [
      { actual: 150000, budget: 140000, variance: 7.1 },
      { actual: 120000, budget: 115000, variance: 4.3 },
      { actual: 30000, budget: 25000, variance: 20.0 },
      { actual: 75000, budget: 70000, variance: 7.1 }
    ],
    // Administration
    [
      { actual: 50000, budget: 50000, variance: 0.0 },
      { actual: 170000, budget: 160000, variance: 6.3 },
      { actual: -120000, budget: -110000, variance: 9.1 },
      { actual: 20000, budget: 25000, variance: -20.0 }
    ]
  ],
  thresholds: {
    good: 5,
    warning: 0,
    danger: -5
  },
  colors: {
    good: '#48BB78',
    warning: '#ED8936',
    danger: '#E53E3E',
    neutral: '#A0AEC0'
  }
};
