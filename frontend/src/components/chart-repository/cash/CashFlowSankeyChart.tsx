import React from "react"
import { ResponsiveSankey } from "@nivo/sankey"

import { CHART_HEIGHT } from "@/components/CardComponents"

// For input data from the application
interface InputNode {
  name: string
  id?: string
  nodeColor?: string
  [key: string]: any
}

interface InputLink {
  source: string | InputNode
  target: string | InputNode
  value: number
  color?: string
  [key: string]: any
}

const CashFlowSankeyChart = ({
  data,
}: {
  data: {
    nodes: InputNode[]
    links: InputLink[]
  }
}) => {
  // Format currency for tooltip
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(Math.abs(value))
  }

  // Transform data to match @nivo/sankey format if needed
  const transformData = () => {
    // Map nodes to ensure they have the required 'id' property
    const nodes = data.nodes.map((node) => ({
      id: node.id || node.name,
    }))

    // Map links to ensure they have the required source/target as strings
    const links = data.links.map((link) => ({
      source:
        typeof link.source === "string"
          ? link.source
          : link.source.id || link.source.name,
      target:
        typeof link.target === "string"
          ? link.target
          : link.target.id || link.target.name,
      value: Math.abs(link.value), // Sankey diagrams typically use absolute values
    }))

    return { nodes, links }
  }

  const sankeyData = transformData()

  // Calculate cash flows for a specific node
  const calculateNodeCashFlows = (nodeId: string) => {
    const originalLinks = data.links

    // Cash in: positive values flowing TO this node
    const cashIn = originalLinks
      .filter((link) => {
        const targetId =
          typeof link.target === "string"
            ? link.target
            : link.target.id || link.target.name
        return targetId === nodeId && link.value > 0
      })
      .reduce((sum, link) => sum + link.value, 0)

    // Cash out: negative values flowing FROM this node (make positive for display)
    const cashOut = originalLinks
      .filter((link) => {
        const sourceId =
          typeof link.source === "string"
            ? link.source
            : link.source.id || link.source.name
        return sourceId === nodeId && link.value < 0
      })
      .reduce((sum, link) => sum + Math.abs(link.value), 0)

    // Net change: cash in minus cash out
    const netChange = cashIn - cashOut

    return { cashIn, cashOut, netChange }
  }

  return (
    <div className="flex w-full flex-col" style={{ height: CHART_HEIGHT }}>
      <ResponsiveSankey
        data={sankeyData}
        align="justify"
        colors={{ scheme: "category10" }}
        nodeOpacity={1}
        nodeThickness={15}
        nodeSpacing={10}
        nodeBorderWidth={0}
        nodeBorderColor={{ from: "color", modifiers: [["darker", 0.8]] }}
        linkOpacity={0.5}
        linkContract={1}
        enableLinkGradient={true}
        labelPosition="outside"
        labelOrientation="horizontal"
        labelPadding={16}
        labelTextColor={{ from: "color", modifiers: [["darker", 1]] }}
        nodeTooltip={({ node }: { node: any }) => {
          const { cashIn, cashOut, netChange } = calculateNodeCashFlows(node.id)

          return (
            <div className="rounded border border-gray-200 bg-white p-3 shadow">
              <div className="mb-2 font-bold">{node.id}</div>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-green-600">Cash In:</span>
                  <span className="font-medium">{formatCurrency(cashIn)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-red-600">Cash Out:</span>
                  <span className="font-medium">{formatCurrency(cashOut)}</span>
                </div>
                <div className="flex justify-between border-t pt-1">
                  <span className="font-semibold">Net Change:</span>
                  <span
                    className={`font-semibold ${netChange >= 0 ? "text-green-600" : "text-red-600"}`}
                  >
                    {netChange >= 0 ? "+" : ""}
                    {formatCurrency(netChange)}
                  </span>
                </div>
              </div>
            </div>
          )
        }}
        linkTooltip={({ link }: { link: any }) => (
          <div className="rounded border border-gray-200 bg-white p-2 shadow">
            <div className="font-bold">
              {link.source.id} → {link.target.id}
            </div>
            <div>Amount: {formatCurrency(link.value)}</div>
          </div>
        )}
      />
    </div>
  )
}

export default CashFlowSankeyChart
