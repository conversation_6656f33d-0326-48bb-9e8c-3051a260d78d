import React from "react"
import {
  Area,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface CashForecastChartProps {
  data: {
    weeks: string[]
    forecast: {
      name: string
      data: number[]
      color: string
    }
    actual: {
      name: string
      data: (number | null)[]
      color: string
    }
    confidenceBands: {
      upper: number[]
      lower: number[]
      color: string
    }
  }
}

interface ChartDataPoint {
  week: string
  forecast?: number
  actual?: number | null
  confidenceUpper?: number
  confidenceLower?: number
}

const CashForecastChart: React.FC<CashForecastChartProps> = ({ data }) => {
  // Transform data for Recharts
  const transformData = (): ChartDataPoint[] => {
    return data.weeks.map((week, index) => {
      return {
        week,
        forecast: data.forecast.data[index],
        actual: data.actual.data[index],
        confidenceUpper: data.confidenceBands.upper[index],
        confidenceLower: data.confidenceBands.lower[index],
      }
    })
  }

  const chartData = transformData()

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-md border border-gray-200 bg-white p-2 shadow-lg">
          <p className="font-semibold">Week: {label}</p>
          {payload.map((entry: any, index: number) => {
            // Skip confidence bands in tooltip
            if (
              entry.dataKey === "confidenceUpper" ||
              entry.dataKey === "confidenceLower"
            ) {
              return null
            }

            return (
              <p key={`item-${index}`} style={{ color: entry.color }}>
                {entry.dataKey === "forecast" ? "Forecast" : "Actual"}:{" "}
                {formatAbbreviatedCurrency(entry.value, 0)}
              </p>
            )
          })}
        </div>
      )
    }
    return null
  }

  // Custom dot for actual data points
  const CustomActualDot = (props: any) => {
    const { cx, cy, payload } = props

    // Only render dot if actual value exists
    if (payload.actual !== null && payload.actual !== undefined) {
      return <circle cx={cx} cy={cy} r={4} fill={data.actual.color} />
    }

    return null
  }

  // Custom dot for forecast data points
  const CustomForecastDot = (props: any) => {
    const { cx, cy } = props
    return <circle cx={cx} cy={cy} r={3} fill={data.forecast.color} />
  }

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <ComposedChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" vertical={false} />
        <XAxis
          dataKey="week"
          angle={-45}
          textAnchor="end"
          height={60}
          tick={{ fontSize: 12 }}
        />
        <YAxis
          tickFormatter={(value) => formatAbbreviatedCurrency(value, 0)}
          tick={{ fontSize: 12 }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 8 }}
        />

        {/* Confidence bands as area */}
        <Area
          type="monotone"
          dataKey="confidenceUpper"
          stroke="none"
          fill={data.confidenceBands.color}
          fillOpacity={0.4}
          activeDot={false}
          stackId="1"
          legendType="none"
        />
        <Area
          type="monotone"
          dataKey="confidenceLower"
          stroke="none"
          fill={data.confidenceBands.color}
          fillOpacity={0}
          activeDot={false}
          stackId="1"
          legendType="none"
        />

        {/* Forecast line */}
        <Line
          type="monotone"
          dataKey="forecast"
          name="Forecast"
          stroke={data.forecast.color}
          strokeWidth={2}
          strokeDasharray="5 5"
          dot={<CustomForecastDot />}
          activeDot={{ r: 5 }}
          isAnimationActive={false}
        />

        {/* Actual line */}
        <Line
          type="monotone"
          dataKey="actual"
          name="Actual"
          stroke={data.actual.color}
          strokeWidth={2}
          dot={<CustomActualDot />}
          activeDot={{ r: 6 }}
          connectNulls
          isAnimationActive={false}
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default CashForecastChart
